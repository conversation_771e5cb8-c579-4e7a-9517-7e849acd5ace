// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Multi-Connection Tests - Server handling multiple concurrent clients

#include "../test_framework/test_base.h"
#include "../test_framework/test_utils.h"
#include "../test_declarations.h"
#include <thread>
#include <atomic>
#include <vector>
#include <mutex>
#include <condition_variable>
#include <future>
#include <chrono>

// ============================================================================
// Multi-Connection Concurrency Tests
// ============================================================================

class TestMultipleClientConnections : public TestBase {
private:
    std::atomic<bool> server_ready{false};
    std::atomic<bool> test_completed{false};
    std::atomic<int> clients_connected{0};
    std::atomic<int> clients_served{0};
    
public:
    TestMultipleClientConnections() : TestBase("Multiple Client Connections") {}
    
    TestCategory GetCategory() const override { return CATEGORY_CONCURRENCY; }
    
    bool Execute() override {
        TestLogger::Log(LOG_INFO, "Testing server handling multiple concurrent client connections");
        
        const int num_clients = 10;
        int test_port = GetAvailablePort();
        
        // Start server thread
        std::thread server_thread([this, test_port, num_clients]() {
            MultiClientServerFunction(test_port, num_clients);
        });
        
        // Wait for server to be ready
        int wait_count = 0;
        while (!server_ready && wait_count < 100) {
            SleepMs(50);
            wait_count++;
        }
        
        CLASS_TEST_ASSERT(server_ready, "Server should be ready within 5 seconds");
        
        // Launch multiple clients concurrently
        std::vector<std::future<bool>> client_futures;
        
        for (int i = 0; i < num_clients; ++i) {
            client_futures.push_back(std::async(std::launch::async, [this, test_port, i]() {
                return ClientFunction(test_port, i);
            }));
        }
        
        // Wait for all clients to complete
        int successful_clients = 0;
        for (auto& future : client_futures) {
            if (future.get()) {
                successful_clients++;
            }
        }
        
        // Signal completion and wait for server
        test_completed = true;
        if (server_thread.joinable()) {
            server_thread.join();
        }
        
        TestLogger::Log(LOG_INFO, "Clients connected: " + std::to_string(clients_connected.load()));
        TestLogger::Log(LOG_INFO, "Clients served: " + std::to_string(clients_served.load()));
        TestLogger::Log(LOG_INFO, "Successful clients: " + std::to_string(successful_clients));
        
        CLASS_TEST_ASSERT(successful_clients >= num_clients * 0.8, 
                         "At least 80% of clients should succeed");
        CLASS_TEST_ASSERT_EQ(clients_connected.load(), clients_served.load(),
                            "All connected clients should be served");
        
        // Update metrics
        metrics_.connections_created = clients_connected;
        metrics_.bytes_sent = successful_clients * 20; // Approximate
        metrics_.bytes_received = successful_clients * 20;
        
        TestLogger::Log(LOG_INFO, "Multiple client connections test completed successfully");
        return true;
    }
    
private:
    void MultiClientServerFunction(int port, int expected_clients) {
        SocketOptions options = CreateSocketOptions(MODE_SERVER);
        P2P_SOCKET server_socket = P2pCreate(&options);
        if (!server_socket) return;
        
        P2pBind(server_socket, "0.0.0.0", port);
        if (P2pListen(server_socket) != 0) {
            P2pClose(server_socket);
            return;
        }
        
        server_ready = true;
        TestLogger::Log(LOG_DEBUG, "Server listening on port " + std::to_string(port));
        
        std::vector<std::thread> client_handlers;
        
        while (!test_completed && clients_connected < expected_clients) {
            char client_ip[128] = {0};
            int client_port = 0;
            P2P_SOCKET accepted_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);
            
            if (accepted_socket) {
                clients_connected++;
                TestLogger::Log(LOG_DEBUG, "Accepted client " + std::to_string(clients_connected.load()) + 
                              " from " + std::string(client_ip) + ":" + std::to_string(client_port));
                
                client_handlers.emplace_back([this, accepted_socket]() {
                    HandleMultiClient(accepted_socket);
                });
            }
        }
        
        // Wait for all client handlers to complete
        for (auto& handler : client_handlers) {
            if (handler.joinable()) {
                handler.join();
            }
        }
        
        P2pClose(server_socket);
        TestLogger::Log(LOG_DEBUG, "Server shutdown complete");
    }
    
    void HandleMultiClient(P2P_SOCKET client_socket) {
        try {
            // Read client ID - use a reasonable buffer size to handle variable length client IDs
            char buffer[256] = {0};
            // Read up to buffer size - 1 to ensure null termination space
            int bytes_read = P2pRead(client_socket, buffer, sizeof(buffer) - 1);

            if (bytes_read > 0) {
                std::string client_data(buffer, bytes_read);
                TestLogger::Log(LOG_DEBUG, "Server received: " + client_data);

                // Process the request (simulate some work)
                SleepMs(10 + (rand() % 20)); // 10-30ms processing time

                // Send response
                std::string response = "Response to " + client_data;
                int bytes_written = P2pWrite(client_socket, response.c_str(), response.length());

                if (bytes_written > 0) {
                    clients_served++;
                    TestLogger::Log(LOG_DEBUG, "Server sent response to client");
                }
            }
        } catch (...) {
            TestLogger::Log(LOG_ERROR, "Exception in client handler");
        }

        P2pClose(client_socket);
    }
    
    bool ClientFunction(int port, int client_id) {
        SocketOptions options = CreateSocketOptions(MODE_CLIENT);
        P2P_SOCKET client_socket = P2pCreate(&options);
        if (!client_socket) return false;
        
        P2pSetConnTimeout(client_socket, 5000);
        
        // Add small random delay to spread out connections
        SleepMs(rand() % 100);
        
        if (P2pConnect(client_socket, "127.0.0.1", port) != 0) {
            TestLogger::Log(LOG_DEBUG, "Client " + std::to_string(client_id) + " failed to connect");
            P2pClose(client_socket);
            return false;
        }
        
        // Send client identification
        std::string client_data = "Client" + std::to_string(client_id) + "Data";
        int bytes_written = P2pWrite(client_socket, client_data.c_str(), client_data.length());
        
        if (bytes_written != (int)client_data.length()) {
            TestLogger::Log(LOG_DEBUG, "Client " + std::to_string(client_id) + " failed to send data");
            P2pClose(client_socket);
            return false;
        }
        
        // Read response
        std::string expected_response = "Response to " + client_data;
        char read_buffer[256] = {0};
        int bytes_read = P2pRead(client_socket, read_buffer, expected_response.length());
        
        bool success = (bytes_read == (int)expected_response.length() && 
                       std::string(read_buffer, bytes_read) == expected_response);
        
        if (success) {
            TestLogger::Log(LOG_DEBUG, "Client " + std::to_string(client_id) + " completed successfully");
        } else {
            TestLogger::Log(LOG_DEBUG, "Client " + std::to_string(client_id) + " failed response verification");
        }
        
        P2pClose(client_socket);
        return success;
    }
};

class TestConnectionStorm : public TestBase {
private:
    std::atomic<bool> server_ready{false};
    std::atomic<bool> test_completed{false};
    std::atomic<int> connections_accepted{0};
    std::atomic<int> connections_processed{0};
    
public:
    TestConnectionStorm() : TestBase("Connection Storm Test") {}
    
    TestCategory GetCategory() const override { return CATEGORY_CONCURRENCY; }
    
    bool Execute() override {
        TestLogger::Log(LOG_INFO, "Testing server under connection storm conditions");
        
        const int num_clients = 20;
        const int connections_per_client = 5;
        int test_port = GetAvailablePort();
        
        // Start server thread
        std::thread server_thread([this, test_port]() {
            ConnectionStormServerFunction(test_port);
        });
        
        // Wait for server to be ready
        int wait_count = 0;
        while (!server_ready && wait_count < 100) {
            SleepMs(50);
            wait_count++;
        }
        
        CLASS_TEST_ASSERT(server_ready, "Server should be ready within 5 seconds");
        
        // Launch connection storm
        std::vector<std::future<int>> client_futures;
        
        auto storm_start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < num_clients; ++i) {
            client_futures.push_back(std::async(std::launch::async, [this, test_port, connections_per_client, i]() {
                return StormClientFunction(test_port, connections_per_client, i);
            }));
        }
        
        // Wait for all clients to complete
        int total_successful_connections = 0;
        for (auto& future : client_futures) {
            total_successful_connections += future.get();
        }
        
        auto storm_end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(storm_end - storm_start);
        
        // Signal completion and wait for server
        test_completed = true;
        if (server_thread.joinable()) {
            server_thread.join();
        }
        
        int expected_connections = num_clients * connections_per_client;
        double connection_rate = (double)total_successful_connections / (duration.count() / 1000.0);
        
        TestLogger::Log(LOG_INFO, "Storm duration: " + std::to_string(duration.count()) + "ms");
        TestLogger::Log(LOG_INFO, "Connections accepted: " + std::to_string(connections_accepted.load()));
        TestLogger::Log(LOG_INFO, "Connections processed: " + std::to_string(connections_processed.load()));
        TestLogger::Log(LOG_INFO, "Successful connections: " + std::to_string(total_successful_connections) + 
                       "/" + std::to_string(expected_connections));
        TestLogger::Log(LOG_INFO, "Connection rate: " + std::to_string(connection_rate) + " conn/sec");
        
        CLASS_TEST_ASSERT(total_successful_connections >= expected_connections * 0.7, 
                         "At least 70% of connections should succeed under storm conditions");
        
        // Update metrics
        metrics_.connections_created = total_successful_connections;
        metrics_.bytes_sent = total_successful_connections * 10;
        metrics_.bytes_received = total_successful_connections * 10;
        
        TestLogger::Log(LOG_INFO, "Connection storm test completed successfully");
        return true;
    }
    
private:
    void ConnectionStormServerFunction(int port) {
        SocketOptions options = CreateSocketOptions(MODE_SERVER);
        P2P_SOCKET server_socket = P2pCreate(&options);
        if (!server_socket) return;
        
        P2pBind(server_socket, "0.0.0.0", port);
        if (P2pListen(server_socket) != 0) {
            P2pClose(server_socket);
            return;
        }
        
        server_ready = true;
        TestLogger::Log(LOG_DEBUG, "Storm server ready on port " + std::to_string(port));
        
        std::vector<std::thread> handlers;
        
        while (!test_completed) {
            char client_ip[128] = {0};
            int client_port = 0;
            P2P_SOCKET accepted_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);
            
            if (accepted_socket) {
                connections_accepted++;
                
                // Handle client in separate thread for maximum concurrency
                handlers.emplace_back([this, accepted_socket]() {
                    HandleStormClient(accepted_socket);
                });
                
                // Limit number of concurrent handlers to prevent resource exhaustion
                if (handlers.size() > 50) {
                    // Clean up completed handlers
                    handlers.erase(
                        std::remove_if(handlers.begin(), handlers.end(),
                            [](std::thread& t) {
                                if (t.joinable()) {
                                    t.join();
                                    return true;
                                }
                                return false;
                            }),
                        handlers.end()
                    );
                }
            }
        }
        
        // Wait for remaining handlers
        for (auto& handler : handlers) {
            if (handler.joinable()) {
                handler.join();
            }
        }
        
        P2pClose(server_socket);
    }
    
    void HandleStormClient(P2P_SOCKET client_socket) {
        char buffer[64] = {0};
        int bytes_read = P2pRead(client_socket, buffer, 10);
        
        if (bytes_read > 0) {
            // Minimal processing - just echo back
            P2pWrite(client_socket, buffer, bytes_read);
            connections_processed++;
        }
        
        P2pClose(client_socket);
    }
    
    int StormClientFunction(int port, int num_connections, int client_id) {
        int successful_connections = 0;
        
        for (int i = 0; i < num_connections; ++i) {
            SocketOptions options = CreateSocketOptions(MODE_CLIENT);
            P2P_SOCKET client_socket = P2pCreate(&options);
            if (!client_socket) continue;
            
            P2pSetConnTimeout(client_socket, 2000); // Shorter timeout for storm
            
            if (P2pConnect(client_socket, "127.0.0.1", port) == 0) {
                std::string data = "C" + std::to_string(client_id) + "D" + std::to_string(i);
                
                if (P2pWrite(client_socket, data.c_str(), 10) == 10) {
                    char response[64] = {0};
                    if (P2pRead(client_socket, response, 10) == 10) {
                        successful_connections++;
                    }
                }
            }
            
            P2pClose(client_socket);
            
            // Small delay between connections from same client
            SleepMs(1);
        }
        
        return successful_connections;
    }
};

// Export test functions for main test runner
std::vector<std::unique_ptr<TestBase>> CreateMultiConnectionTests() {
    std::vector<std::unique_ptr<TestBase>> tests;
    
    tests.push_back(std::make_unique<TestMultipleClientConnections>());
    tests.push_back(std::make_unique<TestConnectionStorm>());
    
    return tests;
}
