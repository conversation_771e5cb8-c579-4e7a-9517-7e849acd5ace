#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <cstring>
#include "src/p2psocket/p2psocket.h"

// Test to verify forced send strategy for large buffers
int main() {
    std::cout << "Testing forced send strategy for large buffers..." << std::endl;
    
    // Initialize socket options
    SocketOptions serverOpts = {0};
    serverOpts.mode = MODE_SERVER;
    serverOpts.type = TYPE_QUIC;
    serverOpts.buffer_size = 1024 * 1024; // 1MB buffer
    
    SocketOptions clientOpts = {0};
    clientOpts.mode = MODE_CLIENT;
    clientOpts.type = TYPE_QUIC;
    clientOpts.buffer_size = 1024 * 1024; // 1MB buffer
    
    // Create server socket
    P2P_SOCKET server = QuicCreate(&serverOpts);
    if (server == NULL) {
        std::cout << "Failed to create server socket" << std::endl;
        return -1;
    }
    
    // Set non-direct send mode to test our forced send implementation
    QuicSetDirectSendMode(server, 0);
    
    // Bind and listen
    if (QuicBind(server, "127.0.0.1", 12346) != 0) {
        std::cout << "Failed to bind server" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    if (QuicListen(server) != 0) {
        std::cout << "Failed to listen" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    std::cout << "Server listening on 127.0.0.1:12346" << std::endl;
    
    // Create client socket
    P2P_SOCKET client = QuicCreate(&clientOpts);
    if (client == NULL) {
        std::cout << "Failed to create client socket" << std::endl;
        QuicRelease(server);
        return -1;
    }
    
    // Set non-direct send mode for client too
    QuicSetDirectSendMode(client, 0);
    
    // Start server thread
    std::thread serverThread([server]() {
        // Accept connection
        P2P_SOCKET clientConn = QuicAccept(server, 30000); // 30 second timeout
        if (!clientConn) {
            std::cout << "Server: Failed to accept connection" << std::endl;
            return;
        }
        
        std::cout << "Server: Connection accepted" << std::endl;
        
        // Set to non-direct send mode to test forced send
        QuicSetDirectSendMode(clientConn, 0);
        
        // Test 1: Send a very large buffer that exceeds typical flow control window
        const int largeBufferSize = 5 * 1024 * 1024; // 5MB - larger than typical 2MB ideal buffer
        std::vector<char> largeBuffer(largeBufferSize, 'L');
        
        std::cout << "Server: Sending large buffer (" << largeBufferSize << " bytes)..." << std::endl;
        
        int result = QuicWrite(clientConn, largeBuffer.data(), largeBufferSize);
        if (result > 0) {
            std::cout << "Server: Large buffer sent successfully, bytes: " << result << std::endl;
        } else {
            std::cout << "Server: Failed to send large buffer, result: " << result << std::endl;
        }
        
        // Test 2: Send multiple smaller buffers after the large one
        const int smallBufferSize = 1024;
        std::vector<char> smallBuffer(smallBufferSize, 'S');
        
        for (int i = 0; i < 3; i++) {
            std::cout << "Server: Sending small buffer " << (i+1) << "..." << std::endl;
            result = QuicWrite(clientConn, smallBuffer.data(), smallBufferSize);
            if (result > 0) {
                std::cout << "Server: Small buffer " << (i+1) << " sent, bytes: " << result << std::endl;
            } else {
                std::cout << "Server: Failed to send small buffer " << (i+1) << ", result: " << result << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        std::cout << "Server: All sends completed" << std::endl;
        
        // Keep connection alive for a bit
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        QuicRelease(clientConn);
    });
    
    // Give server time to start
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // Connect to server
    if (QuicConnect(client, "127.0.0.1", 12346, 10000) != 0) {
        std::cout << "Client: Failed to connect to server" << std::endl;
        serverThread.join();
        QuicRelease(client);
        QuicRelease(server);
        return -1;
    }
    
    std::cout << "Client: Connected to server" << std::endl;
    
    // Receive data
    char recvBuffer[8192];
    int totalReceived = 0;
    int largeBufferReceived = 0;
    int smallBuffersReceived = 0;
    
    std::cout << "Client: Starting to receive data..." << std::endl;
    
    // Receive for a reasonable amount of time
    auto startTime = std::chrono::steady_clock::now();
    auto timeout = std::chrono::seconds(10);
    
    while (std::chrono::steady_clock::now() - startTime < timeout) {
        int received = QuicRead(client, recvBuffer, sizeof(recvBuffer), 1000); // 1 second timeout
        if (received > 0) {
            totalReceived += received;
            
            // Count different types of data
            for (int i = 0; i < received; i++) {
                if (recvBuffer[i] == 'L') {
                    largeBufferReceived++;
                } else if (recvBuffer[i] == 'S') {
                    smallBuffersReceived++;
                }
            }
            
            std::cout << "Client: Received " << received << " bytes (total: " << totalReceived << ")" << std::endl;
        } else if (received == 0) {
            // No data available, continue waiting
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } else {
            std::cout << "Client: Read error: " << received << std::endl;
            break;
        }
    }
    
    std::cout << "Client: Finished receiving. Total: " << totalReceived << " bytes" << std::endl;
    std::cout << "Client: Large buffer data: " << largeBufferReceived << " bytes" << std::endl;
    std::cout << "Client: Small buffer data: " << smallBuffersReceived << " bytes" << std::endl;
    
    // Cleanup
    serverThread.join();
    QuicRelease(client);
    QuicRelease(server);
    
    // Evaluate results
    bool testPassed = true;
    
    if (largeBufferReceived == 0) {
        std::cout << "FAIL: No large buffer data received - forced send may not be working" << std::endl;
        testPassed = false;
    } else {
        std::cout << "PASS: Large buffer data received - forced send appears to be working" << std::endl;
    }
    
    if (smallBuffersReceived == 0) {
        std::cout << "FAIL: No small buffer data received - queue may be blocked" << std::endl;
        testPassed = false;
    } else {
        std::cout << "PASS: Small buffer data received - queue not blocked after large buffer" << std::endl;
    }
    
    if (testPassed) {
        std::cout << "SUCCESS: Forced send strategy test passed!" << std::endl;
        return 0;
    } else {
        std::cout << "FAILURE: Forced send strategy test failed!" << std::endl;
        return -1;
    }
}
