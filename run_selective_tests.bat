@echo off
REM Selective test runner for p2psocket interface
REM This script demonstrates how to run individual tests

echo ========================================
echo P2P Socket Selective Test Runner
echo ========================================

echo.
echo ========================================
echo Running Individual Tests
echo ========================================
cd build_p2psocket\bin\Release
echo.
echo 1. Testing Basic Operations...
testp2psocket.exe -test basic
set BASIC_RESULT=%errorlevel%
if %BASIC_RESULT% equ 0 (
    echo [OK] Basic operations test PASSED
) else (
    echo [FAIL] Basic operations test FAILED
    echo Stopping here - fix basic operations first
    pause
    exit /b %BASIC_RESULT%
)

echo.
echo 2. Testing Invalid Parameters...
testp2psocket.exe -test invalid
set INVALID_RESULT=%errorlevel%
if %INVALID_RESULT% equ 0 (
    echo [OK] Invalid parameters test PASSED
) else (
    echo [FAIL] Invalid parameters test FAILED
)

echo.
echo 3. Testing Socket Binding...
testp2psocket.exe -test binding
set BINDING_RESULT=%errorlevel%
if %BINDING_RESULT% equ 0 (
    echo [OK] Socket binding test PASSED
) else (
    echo [FAIL] Socket binding test FAILED
)

echo.
echo 4. Testing Socket Settings...
testp2psocket.exe -test settings
set SETTINGS_RESULT=%errorlevel%
if %SETTINGS_RESULT% equ 0 (
    echo [OK] Socket settings test PASSED
) else (
    echo [FAIL] Socket settings test FAILED
)

echo.
echo 5. Testing Polling Operations...
testp2psocket.exe -test polling
set POLLING_RESULT=%errorlevel%
if %POLLING_RESULT% equ 0 (
    echo [OK] Polling operations test PASSED
) else (
    echo [FAIL] Polling operations test FAILED
)

echo.
echo 6. Testing Server-Client Connection...
testp2psocket.exe -test connection
set CONNECTION_RESULT=%errorlevel%
if %CONNECTION_RESULT% equ 0 (
    echo [OK] Connection test PASSED
) else (
    echo [FAIL] Connection test FAILED
)
echo.
echo 7. Testing Vectored I/O...
testp2psocket.exe -test vectored
set VECTORED_RESULT=%errorlevel%
if %VECTORED_RESULT% equ 0 (
    echo [OK] Vectored I/O test PASSED
) else (
    echo [FAIL] Vectored I/O test FAILED
)

echo.
echo 8. Testing Multi-Client Connections...
testp2psocket.exe -test multiclient
set MULTICLIENT_RESULT=%errorlevel%
if %MULTICLIENT_RESULT% equ 0 (
    echo [OK] Multi-client test PASSED
) else (
    echo [FAIL] Multi-client test FAILED
)

echo.
echo 9. Testing Edge Cases...
testp2psocket.exe -test edge
set EDGE_RESULT=%errorlevel%
if %EDGE_RESULT% equ 0 (
    echo [OK] Edge cases test PASSED
) else (
    echo [FAIL] Edge cases test FAILED
)

echo.
echo ========================================
echo Complete Test Results Summary
echo ========================================
echo Basic Operations: %BASIC_RESULT%
echo Invalid Parameters: %INVALID_RESULT%
echo Socket Binding: %BINDING_RESULT%
echo Socket Settings: %SETTINGS_RESULT%
echo Polling Operations: %POLLING_RESULT%
echo Connection Test: %CONNECTION_RESULT%
echo Vectored I/O: %VECTORED_RESULT%
echo Multi-Client: %MULTICLIENT_RESULT%
echo Edge Cases: %EDGE_RESULT%

REM Calculate total failures
set /a TOTAL_FAILURES=%BASIC_RESULT%+%INVALID_RESULT%+%BINDING_RESULT%+%SETTINGS_RESULT%+%POLLING_RESULT%+%CONNECTION_RESULT%+%VECTORED_RESULT%+%MULTICLIENT_RESULT%+%EDGE_RESULT%

if %TOTAL_FAILURES% equ 0 (
    echo.
    echo *** ALL TESTS PASSED! ***
    echo The p2psocket interface is working correctly.
) else (
    echo.
    echo *** SOME TESTS FAILED ***
    echo Total failed tests: %TOTAL_FAILURES%
    echo Please check the output above for details.
)


echo.
echo To run a specific test manually:
echo   testp2psocket.exe -test ^<testname^>
echo.
echo Available tests: basic, invalid, binding, settings, polling,
echo                  connection, vectored, multiclient, edge, all
echo.
cd ..\..\..
echo Press any key to exit...
pause >nul
exit /b %TOTAL_FAILURES%
